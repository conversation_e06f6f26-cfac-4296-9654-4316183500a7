<?php
// Debug script cho vấn đề đăng nhập
require_once($_SERVER['DOCUMENT_ROOT'].'/config/database.php');

echo "<h2>Debug Login Issues</h2>";

// Kiểm tra database connection
echo "<h3>1. Database Connection Test</h3>";
if ($thanhdieudb) {
    echo "✅ Database kết nối thành công<br>";
    
    // Kiểm tra bảng users
    $result = $thanhdieudb->query("SHOW TABLES LIKE 'users'");
    if ($result && $result->num_rows > 0) {
        echo "✅ Bảng 'users' tồn tại<br>";
        
        // Đếm số users
        $count_result = $thanhdieudb->query("SELECT COUNT(*) as total FROM users");
        if ($count_result) {
            $count = $count_result->fetch_assoc();
            echo "📊 Tổng số users: " . $count['total'] . "<br>";
        }
        
        // First, let's see what columns actually exist
        echo "<h4>Table Structure:</h4>";
        $describe_result = $thanhdieudb->query("DESCRIBE users");
        if ($describe_result && $describe_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while($row = $describe_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Now show some sample users with available columns (excluding password)
        echo "<h4>Sample Users:</h4>";
        $users_result = $thanhdieudb->query("SELECT user_id, username, email, rank, banned, ngaythamgia FROM users LIMIT 3");
        if ($users_result && $users_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            
            // Get field names dynamically
            $fields = $users_result->fetch_fields();
            echo "<tr>";
            foreach($fields as $field) {
                if ($field->name !== 'password') { // Skip password column
                    echo "<th>" . $field->name . "</th>";
                }
            }
            echo "</tr>";
            
            // Reset result pointer and show data
            $users_result->data_seek(0);
            while($row = $users_result->fetch_assoc()) {
                echo "<tr>";
                foreach($row as $key => $value) {
                    if ($key !== 'password') { // Skip password column
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "❌ Bảng 'users' không tồn tại<br>";
    }
} else {
    echo "❌ Không thể kết nối database<br>";
}

// Kiểm tra session
echo "<h3>2. Session Test</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session đã được khởi tạo<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ Session chưa được khởi tạo<br>";
}

// Kiểm tra cookies
echo "<h3>3. Cookie Test</h3>";
if (isset($_COOKIE['ssk'])) {
    echo "✅ Cookie 'ssk' tồn tại<br>";
    echo "Cookie value length: " . strlen($_COOKIE['ssk']) . " characters<br>";
    
    // Thử decrypt cookie
    global $wtSecurity;
    if (isset($wtSecurity)) {
        $decrypted = $wtSecurity->decrypt($_COOKIE['ssk']);
        if ($decrypted) {
            echo "✅ Cookie có thể được decrypt thành công<br>";
            echo "Decrypted value: " . $decrypted . "<br>";
        } else {
            echo "❌ Cookie không thể decrypt<br>";
        }
    }
} else {
    echo "❌ Cookie 'ssk' không tồn tại<br>";
}

// Kiểm tra localhost detection
echo "<h3>4. Localhost Detection</h3>";
$isLocalhost = (
    $_SERVER['HTTP_HOST'] === 'localhost' || 
    $_SERVER['HTTP_HOST'] === '127.0.0.1' || 
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
);

if ($isLocalhost) {
    echo "✅ Đang chạy trên localhost<br>";
    echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
    echo "Secure cookie should be: FALSE<br>";
} else {
    echo "❌ Không phải localhost<br>";
    echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
    echo "Secure cookie should be: TRUE<br>";
}

// Kiểm tra key encryption
echo "<h3>5. Encryption Key Test</h3>";
if (isset($TD) && $TD->Setting('key-aes')) {
    echo "✅ AES key được cấu hình<br>";
    echo "Key length: " . strlen($TD->Setting('key-aes')) . " characters<br>";
} else {
    echo "❌ AES key chưa được cấu hình<br>";
}

// Kiểm tra anti-spam settings
echo "<h3>6. Anti-spam Configuration</h3>";
if (isset($TD)) {
    echo "Anti-spam enabled: " . ($TD->Setting('anti-spam') ? 'Yes' : 'No') . "<br>";
    echo "Limit account: " . ($TD->Setting('limit-account') ?? 'N/A') . "<br>";
}

// Test form đăng nhập đơn giản
echo "<h3>7. Simple Login Test</h3>";
?>
<form method="post" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <h4>Test Login Form</h4>
    <input type="hidden" name="_auth" value='{"auth":"user-auth-login"}'>
    <p>
        <label>Username/Email:</label><br>
        <input type="text" name="username" required style="width: 200px;">
    </p>
    <p>
        <label>Password:</label><br>
        <input type="password" name="password" required style="width: 200px;">
    </p>
    <p>
        <input type="checkbox" name="remember-me" value="on"> Remember me
    </p>
    <p>
        <button type="submit">Test Login</button>
    </p>
</form>

<?php
// Xử lý test login
if ($_POST && isset($_POST['_auth'])) {
    echo "<h4>Login Test Result:</h4>";
    
    // Include auth model
    require_once($_SERVER['DOCUMENT_ROOT'].'/server/models/object/users/authModel.php');
    
    $auth = new UserAuth($TD);
    $result = $auth->execute($_POST, []);
    echo "<pre>" . $result . "</pre>";
}

echo "<hr>";
echo "<p>Nếu tất cả các test trên đều pass, thì login should work. Nếu vẫn có vấn đề, check browser console for JavaScript errors.</p>";
?>