-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON> chủ: localhost:3306
-- Th<PERSON><PERSON> gian đã tạo: Th9 16, 2025 lúc 08:59 PM
-- <PERSON><PERSON><PERSON> bản m<PERSON>hụ<PERSON> vụ: 10.11.11-MariaDB
-- <PERSON><PERSON><PERSON> bản PHP: 8.3.9

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> sở dữ liệu: `qhethongtienbano_phuc`
--

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `banned` int(11) NOT NULL DEFAULT 0,
  `rank` varchar(255) NOT NULL DEFAULT 'members',
  `ip` varchar(255) DEFAULT NULL,
  `ngaythamgia` datetime DEFAULT current_timestamp(),
  `email` varchar(255) DEFAULT NULL,
  `sodu` varchar(255) NOT NULL DEFAULT '0',
  `tongtieu` varchar(255) NOT NULL DEFAULT '0',
  `tongnap` varchar(255) NOT NULL DEFAULT '0',
  `hoahong` int(11) NOT NULL DEFAULT 0,
  `avatar` varchar(255) NOT NULL DEFAULT 'avatar.gif',
  `access_key` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `users`
--

INSERT INTO `users` (`user_id`, `username`, `password`, `banned`, `rank`, `ip`, `ngaythamgia`, `email`, `sodu`, `tongtieu`, `tongnap`, `hoahong`, `avatar`, `access_key`) VALUES
(1, 'yuenanyuyingyu', '$2y$07$wZ1T8FAaTghk.wolsuytyuGEADk4iSFYYjegskulXlqqjg.TzJTMG', 0, 'admin', '**************', '2025-06-10 16:52:13', '<EMAIL>', '0', '0', '0', 0, '#dac0f7', '399a5dee342f-3b6b-4538-bb27-7978e7d12593'),
(2, 'zenitmedia', '$2y$07$pr0YDYZrrjv5HMM7J.7.RuK5agUdzmkddaKKn1fDOygPXTjc8UCay', 0, 'members', '**************', '2025-06-10 17:08:35', '<EMAIL>', '0', '0', '0', 0, '#53f236', 'a9c7bfafef92-2007-416a-b216-a048aab3af25');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_dsgoi`
--

CREATE TABLE `ws_dsgoi` (
  `dsgoi_id` int(11) NOT NULL,
  `tengoi` varchar(255) NOT NULL,
  `giagoi` int(11) NOT NULL,
  `gioihanbill` int(11) NOT NULL DEFAULT 0,
  `hansudung` varchar(200) NOT NULL DEFAULT '1',
  `ngaytao` datetime NOT NULL DEFAULT current_timestamp(),
  `trangthai` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Bẫy `ws_dsgoi`
--
DELIMITER $$
CREATE TRIGGER `update_gioihanbill` AFTER UPDATE ON `ws_dsgoi` FOR EACH ROW BEGIN UPDATE ws_plans SET gioihanbill = NEW.gioihanbill WHERE tengoi = CONCAT('vip', NEW.dsgoi_id); END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_freebill`
--

CREATE TABLE `ws_freebill` (
  `freebill_id` int(11) NOT NULL,
  `taikhoan` varchar(255) NOT NULL,
  `thoigian` datetime DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_freebill`
--

INSERT INTO `ws_freebill` (`freebill_id`, `taikhoan`, `thoigian`) VALUES
(1, 'zenitmedia', '2025-06-10 17:11:51');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_history_bank`
--

CREATE TABLE `ws_history_bank` (
  `bank_id` int(11) NOT NULL,
  `username` varchar(255) DEFAULT NULL,
  `loai` varchar(255) NOT NULL,
  `magiaodich` varchar(255) NOT NULL,
  `sotien` int(11) NOT NULL DEFAULT 0,
  `noidung` text DEFAULT NULL,
  `thoigian` datetime NOT NULL DEFAULT current_timestamp(),
  `trangthai` enum('thatbai','thanhcong') DEFAULT 'thatbai'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_history_card`
--

CREATE TABLE `ws_history_card` (
  `card_id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `taikhoan` varchar(255) DEFAULT NULL,
  `loaithe` varchar(50) DEFAULT NULL,
  `mathe` varchar(20) DEFAULT NULL,
  `seriel` varchar(20) DEFAULT NULL,
  `menhgia` int(11) DEFAULT NULL,
  `thoigian` datetime DEFAULT current_timestamp(),
  `trangthai` enum('thanhcong','thatbai','choxuly') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_history_fakebill`
--

CREATE TABLE `ws_history_fakebill` (
  `fakebill_id` int(11) NOT NULL,
  `username` varchar(255) DEFAULT NULL,
  `namebill` varchar(255) NOT NULL,
  `image` text DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `time` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_history_fakebill`
--

INSERT INTO `ws_history_fakebill` (`fakebill_id`, `username`, `namebill`, `image`, `type`, `time`) VALUES
(1, 'zenitmedia', 'SacombankPay', 'a10463df69e52e78372b724471434ec9.jpg', 'Màn Hình Khoá', '2025-06-10 17:11:51');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_history_products`
--

CREATE TABLE `ws_history_products` (
  `history_id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `taikhoan` varchar(255) NOT NULL,
  `giamua` int(11) NOT NULL,
  `ngaymua` datetime NOT NULL DEFAULT current_timestamp(),
  `magiaodich` varchar(255) NOT NULL,
  `trangthai` varchar(255) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_limitbill`
--

CREATE TABLE `ws_limitbill` (
  `limitbill_id` int(11) NOT NULL,
  `taikhoan` varchar(255) NOT NULL,
  `ngaytao` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_logs`
--

CREATE TABLE `ws_logs` (
  `log_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `content` varchar(500) NOT NULL,
  `time` datetime DEFAULT current_timestamp(),
  `action` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_logs`
--

INSERT INTO `ws_logs` (`log_id`, `username`, `content`, `time`, `action`) VALUES
(1, 'yuenanyuyingyu', 'đăng ký tài khoản mới', '2025-06-10 16:52:13', 'Đăng Ký Tài Khoản'),
(2, 'zenitmedia', 'đăng ký tài khoản mới', '2025-06-10 17:08:35', 'Đăng Ký Tài Khoản'),
(3, 'zenitmedia', 'tạo fake bill miễn phí', '2025-06-10 17:11:51', 'Tạo Bill Free');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_newfeeds`
--

CREATE TABLE `ws_newfeeds` (
  `newfeeds_id` int(11) NOT NULL,
  `tieude` varchar(255) NOT NULL,
  `noidung` text NOT NULL,
  `loai` varchar(50) DEFAULT NULL,
  `ngaydang` datetime DEFAULT current_timestamp(),
  `trangthai` tinyint(1) DEFAULT 1
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_newfeeds`
--

INSERT INTO `ws_newfeeds` (`newfeeds_id`, `tieude`, `noidung`, `loai`, `ngaydang`, `trangthai`) VALUES
(1, 'Tin Tức #1', '<img src=\"https://imgur.com/JS3Z97t.gif\" width=\"20\"> Khi quý khách nạp&nbsp;tiền, vui lòng ghi đầy đủ nội dung bank trước khi chuyển, và hãy luôn đọc và&nbsp;để ý các lưu ý mà chúng tôi đã ghi rõ.<img src=\"https://imgur.com/Sd4JMGu.gif\" width=\"20\"> Chúng tôi có bán&nbsp;code fakebill này với giá siêu rẻ cho các khách hàng kinh doanh, mọi chi tiết xin vui lòng liên hệ đến chúng tôi.<br>Tag: fakebill, web fakebill, fake bdsd, web fake cccd, fake cccd, fakebil tạo bill, fake bill miễn phí', 'primary', '2024-06-20 00:00:00', 1),
(2, 'Thông Báo Khẩn Cấp', '<p> <img src=\"https://imgur.com/a54JAh8.gif\" width=\"20\"> Kính Chào Khách Hàng,\r\n\r\n🌪 Hệ thống chúng tôi gặp sự cố không mong muốn, có những tài khoản đã nạp mà bị reset số dư về 0đ hoặc tổng nạp bị reset về 0đ hãy chụp ảnh tài khoản trên web + bill chuyển tiền để admin check và hoàn lại tiền trong web về như\r\n                                            cũ.\r\n\r\n<img src=\"https://imgur.com/Rs7xac5.gif\" width=\"20\"> Đối với các trường hợp nạp mà chưa được cộng tiền vui lòng kiểm tra xem đã ghi nội dung hay chưa, và có đúng hay không, nếu đã đúng nd mà chưa được cộng tiền hãy gửi bill cho admin.\r\n\r\n <a href=\"https://t.me/khanhsiucute\" target=\"_blank\">https://t.me/khanhsiucute</a></p>', 'warning', '2024-07-31 00:00:00', 1),
(3, 'Tin Tức #2', '➡️ Cực hot, siêu giảm giá cho tất cả các gói thành viên VIP <img src=\"https://i.imgur.com/qvSmhvo.png\" width=\"20\">.\r\n🙅 Còn chần chờ gì nữa mà không nâng cấp ngay, tạo bill không có watermark và không giới hạn lần tạo bill.  \r\n<img src=\"https://imgur.com/XnaqI6Z.png\" width=\"20\"> Hiệu lực áp dụng cho cuối tháng này, hãy nhanh tay thuê ngay kẻo bỏ lỡ !!!.', 'info', '2024-07-31 00:00:00', 1),
(4, 'Siêu Giảm Giá', '<img src=\"https://i.imgur.com/stN5Rel.gif\" width=\"20\"> Nhân dịp ngày Quốc tế Bạn gái.\r\n<img src=\"https://i.imgur.com/fmCupKL.png\" width=\"15\"> Chúng tôi hạ giá gói VIP1 xuống chỉ còn 4k, gói VIP2 chỉ còn 10k, đặc biệt gói VIP3 chỉ còn 25k.\r\n👉 Nhanh tay nhấn vào <a href=\"/thue-goi\">Thuê Gói VIP</a> để mua ngay trong hôm nay, áp dụng giảm giá kể từ ngày 01/08 đến cuối ngày 02/08.', 'primary', '2024-08-01 00:00:00', 1),
(5, 'Tin Tức #3', '<img src=\"https://i.imgur.com/aPDbjca.gif\" width=\"20\">&ensp;Đã hết thời gian khuyến mãi giảm giá các gói VIP!!!\n<img src=\"https://i.imgur.com/7MqSOai.png\" width=\"24\">Website sắp ra fake bill biến động số dư, fake cccd nên sẽ tăng giá lên để phù hợp với tính năng này.\n✅ Nền tảng fake bill không giới hạn số lần tạo, tự tin là những dịch vụ hàng đầu về fake bill chuyển khoản, cung cấp các app fakebill chuyên nghiệp.', 'primary', '2024-08-04 00:00:00', 1),
(6, 'Bản Cập Nhật & Vá Lỗi', '<img src=\"https://i.imgur.com/Daw8j7T.gif\" width=\"20\"> Fake biến động số dư đã có mặt trên nền tảng fakebill, quý khách vui lòng kiểm tra và sử dụng.\r\n<img src=\"https://i.imgur.com/TB6V1ww.gif\" width=\"20\"> Trong quá trình sử dụng, quý khách gặp các vấn đề gặp lỗi hoặc cần được sự hỗ trợ, vui lòng liên hệ đến telegram của chúng tôi.\r\n<img src=\"https://i.imgur.com/aPDbjca.gif\" width=\"20\"> Hệ thống vẫn đang nâng cấp và thêm tính năng cho các fakebill ngân hàng. Vui lòng kiên nhẫn chờ đợi.', 'primary', '2024-08-06 00:00:00', 1),
(7, 'Bản Cập Nhật Mới Nhất', '🔔 🔔\r\n💥Cập Nhật Mục Seting Pin, Wifi, Sóng, Tải Thỏ Mới Nhất Ngay Tại Từng Bill Của Web Anh Em Có Thể Lên Xem Mình Sẽ Thông Báo Cả 2 Bên Kênh Tele Này Và Trên Web Cho Anh Em Nào Chưa Vô Kênh. Trải nghiệm ngay!✅', 'info', '2025-03-31 16:03:04', 1),
(8, 'Bản Cập Nhật Mới Nhất', '🔔 🔔\r\n💥THÔNG BÁO Update Bill Số Dư Techcombank Full Giao Diện. Trải nghiệm ngay!✅', 'info', '2025-04-09 23:23:59', 1),
(9, 'Bản Cập Nhật Mới Nhất', '🔔 🔔\r\n💥THÔNG BÁO Update Bill Số Dư MB Bank Full 20 Giao Diện. Trải nghiệm ngay!✅', 'info', '2025-04-09 23:25:08', 1),
(10, 'Bản Cập Nhật Mới Nhất', '🔔 🔔\r\n💥THÔNG BÁO Update Bill Đơn Lưu MB Bank Full 19 Giao Diện MB Bank Cho Anh Em. Trải nghiệm ngay!✅', 'info', '2025-04-09 23:25:36', 1),
(11, 'Update Bidv Giao Diện Mới', 'Đã Update giao diện bill ck BIDV mới nhất,biến động thông báo trên cùng bill !', 'info', '2025-05-27 01:45:33', 1),
(12, 'Cập Nhập Bill Pg Bank', 'Cập nhập Bill PG BANK giao diện mới nhất, HD, Trải nghiệm ngay!', 'info', '2025-05-27 01:46:10', 1),
(13, 'Update All Bill Tp Bank New', 'Update All Bill TP Bank New full hd: gồm bill ck, bđsd. đều hỗ trợ tên và nội dung dài, có hiển thị thông báo biến động. Trải nghiệm ngay!', 'info', '2025-05-27 01:46:45', 1),
(14, 'Cập Nhập Bill Vcb Priority Mới', 'Cập nhập Bill VCB PRIORITY giao diện mới nhất, HD, Trải nghiệm ngay!', 'info', '2025-05-27 01:48:09', 1),
(15, 'Cập Nhật Vcb Galaxy Mới', 'Cập nhập Bill VCB GALAXY giao diện mới nhất, HD, Trải nghiệm ngay!', 'info', '2025-05-27 01:48:41', 1),
(16, 'Cập Nhật Biến Động Acb', 'Cập nhập Bill biến động màn hình khoá ACB giao diện mới nhất, HD, Trải nghiệm ngay!', 'info', '2025-05-27 01:50:13', 1),
(17, 'Cập Nhật Bill Sacombank Mới', 'Cập nhập Bill SaComBank giao diện mới nhất, HD, Trải nghiệm ngay!', 'info', '2025-05-27 01:50:36', 1);

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_otpmailler`
--

CREATE TABLE `ws_otpmailler` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `otp_code` varchar(10) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  `token` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_otpmailler`
--

INSERT INTO `ws_otpmailler` (`id`, `username`, `otp_code`, `created_at`, `expires_at`, `is_used`, `token`) VALUES
(14, 'thienanh37', '661481', '2025-02-07 21:07:57', '2025-02-08 21:07:53', 1, '11a8caa576bd08358507fcf4ca53eac55f8ca4b0d606d91972a06015c72efe70'),
(15, '********', '487503', '2025-02-09 15:24:49', '2025-02-10 15:24:45', 1, '72c99338d378e8902f74c44c7e76b0f4e2c692a8e6c61d9403f2c14f26577916'),
(18, 'khanhhuyen06', '147038', '2025-03-04 23:03:17', '2025-03-05 23:03:13', 1, '85b024f9578e5947b5f3ec08707d27c2dd7cdfe0245bd90b978feed6bb145139'),
(19, 'khanhhuyen06', '394010', '2025-03-04 23:09:25', '2025-03-05 23:09:21', 1, '064158dd3e3ee1d72fd194f4d797dd5a637a8daea4f684ed3805fa16c1e69354'),
(21, 'gunvhj', '456779', '2025-03-14 10:48:55', '2025-03-15 10:48:52', 1, '8fa79363578bf011785e44531c39f1db36c0967661472ef975b32770f0c2cb88'),
(22, 'hoatvu99', '958071', '2025-03-15 20:24:10', '2025-03-16 20:24:06', 1, '5267ae8271d93b7dff265f37b7ebd977a540a46dc1c3e6118523ad6b79144362'),
(23, '********', '838265', '2025-04-03 13:01:18', '2025-04-04 13:01:14', 1, '76818b80a85f192782d53553d68e7ed5ecde342cccdd0ab2ab66abce6961a8fe'),
(28, 'congtan123', '560431', '2025-04-25 10:06:49', '2025-04-26 10:06:45', 1, '8c39e07db64133a34409b1d225592282a968a4be9f6f9b2e18a3e4c1559dc009'),
(29, '0398762421', '663791', '2025-05-05 19:59:59', '2025-05-06 19:59:55', 1, '2eefdc08b5cb284f5678135eff7eb2f6a63156a32ecc29ce8b09c2abee49b849'),
(36, 'cuonghb2007', '813317', '2025-06-04 18:58:40', '2025-06-05 18:58:37', 1, '0141f72e1f38db6f30775aadda0800027cd4a26906d48959fb5e3ea28749c5e2');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_plans`
--

CREATE TABLE `ws_plans` (
  `plans_id` int(11) NOT NULL,
  `taikhoan` varchar(255) DEFAULT NULL,
  `tengoi` varchar(255) DEFAULT NULL,
  `giatien` int(11) NOT NULL DEFAULT 0,
  `gioihanbill` int(11) NOT NULL,
  `ngaymua` datetime DEFAULT NULL,
  `ngayhethan` datetime NOT NULL,
  `trangthai` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_products`
--

CREATE TABLE `ws_products` (
  `id` int(11) NOT NULL,
  `taikhoan` varchar(255) DEFAULT NULL,
  `tieude` varchar(255) DEFAULT NULL,
  `noidung` longtext DEFAULT NULL,
  `hinhthunho` text DEFAULT NULL,
  `anhsanpham` text DEFAULT NULL,
  `giatien` int(11) DEFAULT 0,
  `ngaydang` datetime DEFAULT current_timestamp(),
  `slug` varchar(255) DEFAULT NULL,
  `download_url` varchar(255) NOT NULL,
  `ngaycapnhat` datetime NOT NULL DEFAULT current_timestamp(),
  `trangthai` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_ref`
--

CREATE TABLE `ws_ref` (
  `ref_id` int(11) NOT NULL,
  `magiaodich` varchar(255) DEFAULT NULL,
  `nguoigioithieu` varchar(255) NOT NULL,
  `nguoiduocgioithieu` varchar(255) NOT NULL,
  `thoigian` datetime DEFAULT current_timestamp(),
  `hoahong` int(11) DEFAULT 0,
  `trangthai` enum('thanhcong','thatbai','choxuly') DEFAULT 'choxuly'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_rsakey`
--

CREATE TABLE `ws_rsakey` (
  `rsa_id` int(11) NOT NULL,
  `public_key` text NOT NULL,
  `private_key` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_rsakey`
--

INSERT INTO `ws_rsakey` (`rsa_id`, `public_key`, `private_key`) VALUES
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_scaninfo`
--

CREATE TABLE `ws_scaninfo` (
  `scaninfo_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `loai` varchar(100) NOT NULL,
  `giatien` int(11) NOT NULL DEFAULT 0,
  `ngaytao` datetime DEFAULT current_timestamp(),
  `ngaycapnhat` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `ghichu` text DEFAULT NULL,
  `trangthai` enum('success','pending','failed') NOT NULL DEFAULT 'pending',
  `giatri` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_settings`
--

CREATE TABLE `ws_settings` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT 'Hệ Thống FakeBill',
  `keywords` text DEFAULT NULL,
  `favicon` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `black-ip` text DEFAULT NULL,
  `author` varchar(255) DEFAULT '0',
  `footer` text DEFAULT NULL,
  `bao-tri` int(11) DEFAULT 0,
  `name-site` varchar(255) NOT NULL DEFAULT 'WusTeam',
  `version-code` varchar(11) NOT NULL,
  `zalo` varchar(200) NOT NULL,
  `facebook` varchar(255) NOT NULL,
  `telegram` varchar(220) NOT NULL,
  `og:image` varchar(255) NOT NULL,
  `boxchat` varchar(200) NOT NULL DEFAULT 'https://thanhdieu.com',
  `backupdb` int(11) NOT NULL DEFAULT 1,
  `id-geetest` varchar(255) NOT NULL DEFAULT '0',
  `key-geetest` varchar(255) NOT NULL DEFAULT '0',
  `min-nap` int(11) NOT NULL,
  `partner-id` varchar(255) NOT NULL,
  `partner-key` varchar(255) DEFAULT NULL,
  `is-modal` int(11) NOT NULL,
  `is-log` tinyint(1) NOT NULL DEFAULT 0,
  `modal-content` mediumtext NOT NULL,
  `cache` varchar(255) NOT NULL DEFAULT '1.1.1',
  `key-aes` varchar(255) NOT NULL,
  `navbar-logo` varchar(255) NOT NULL,
  `cuphap-naptien` varchar(255) NOT NULL DEFAULT 'WSNAPTIEN',
  `giataobill` int(11) NOT NULL DEFAULT 0,
  `limit-bill` int(11) NOT NULL DEFAULT 1,
  `time-bill` varchar(255) NOT NULL DEFAULT '0',
  `loader` int(11) NOT NULL DEFAULT 1,
  `mail-user` varchar(255) NOT NULL,
  `mail-pass` varchar(255) NOT NULL,
  `anti-spam` tinyint(1) NOT NULL DEFAULT 1,
  `https` tinyint(1) NOT NULL DEFAULT 0,
  `fuck-devtools` int(11) NOT NULL DEFAULT 0,
  `limit-account` int(11) NOT NULL DEFAULT 1,
  `auto-delete` int(11) NOT NULL DEFAULT 3,
  `khuyen-mai` int(11) NOT NULL DEFAULT 0,
  `giataogiayto` varchar(255) NOT NULL DEFAULT 'vip1',
  `is-captcha` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_settings`
--

INSERT INTO `ws_settings` (`id`, `title`, `keywords`, `favicon`, `description`, `black-ip`, `author`, `footer`, `bao-tri`, `name-site`, `version-code`, `zalo`, `facebook`, `telegram`, `og:image`, `boxchat`, `backupdb`, `id-geetest`, `key-geetest`, `min-nap`, `partner-id`, `partner-key`, `is-modal`, `is-log`, `modal-content`, `cache`, `key-aes`, `navbar-logo`, `cuphap-naptien`, `giataobill`, `limit-bill`, `time-bill`, `loader`, `mail-user`, `mail-pass`, `anti-spam`, `https`, `fuck-devtools`, `limit-account`, `auto-delete`, `khuyen-mai`, `giataogiayto`, `is-captcha`) VALUES
(1, 'Fakebillxz - Trang Web Fakebill Chuyển Khoản Số 1 VN', 'Trang Web Fake Bill, Chuyển Khoản, fake bill Vietinbank, Mbbank, Momo, Vietcombank và nhiều ngân hàng khác website fakebill', '/public/src/vtd/img/logo.png', 'Trang Web Fake Bill, Chuyển Khoản, fake bill Vietinbank, Mbbank, Momo, Vietcombank và nhiều ngân hàng khác', '', 'Nguyễn Việt Anh ', '© 2024, VietKhanh. All Rights Reserved.', 0, 'BANKVN.SHOP', '6.2.4', 'https://zalo.me/**********', 'https://facebook.com', 'https://t.me/+RY0SnfnUGNQ2YmE1', '/public/src/vtd/img/banner.png', 'https://t.me/+RY0SnfnUGNQ2YmE1', 0, 'cd8e3b84507b9a3dc14fe07f58a10959', '46fdffc70f10b5e375b2ed795d0794ca', 5000, '0', '', 0, 0, ' <b class=\"text-red-800\">💥 Kể từ ngày 09/08 mõi tài khoản sẽ nhận được <?=$TD->Setting(\'limit-bill\')?>\n                    lần tạo bill miễn phí hằng ngày!<br /><br />\n                    ✅ Những thành viên không có gói VIP hoặc không nạp tiền, nền tảng sẽ hiện quảng cáo.<br><br />\n                    🤙 Để biết thêm về bản cập nhật này vui lòng xem thêm: <a href=\"https://t.me/wus_team\">Tại đây</a>\n                </b>', '6.1.14', '98bc917b061e52e4dc9e3b655d561627', 'https://files.catbox.moe/cx8zld.jpg', 'NAPTIEN', 2000, 1, '0', 1, '<EMAIL>', 'qgwp qtmj eagf ftcl', 1, 0, 0, 3, 4, 0, 'vip2', 1);

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_spamsms`
--

CREATE TABLE `ws_spamsms` (
  `spamsms_id` int(11) NOT NULL,
  `taikhoan` varchar(255) NOT NULL,
  `sodienthoai` varchar(20) NOT NULL,
  `giatien` int(11) NOT NULL DEFAULT 0,
  `maychu` varchar(255) NOT NULL,
  `thoigian` datetime NOT NULL DEFAULT current_timestamp(),
  `thoigiancho` datetime DEFAULT current_timestamp(),
  `trangthai` varchar(20) NOT NULL DEFAULT 'pending'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_subdomain`
--

CREATE TABLE `ws_subdomain` (
  `domain_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `ten_mien` varchar(255) NOT NULL,
  `hauto` varchar(255) NOT NULL,
  `loai` varchar(50) NOT NULL,
  `gia_tri` varchar(255) NOT NULL,
  `ngaytao` datetime NOT NULL DEFAULT current_timestamp(),
  `ngaycapnhat` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `trangthai` enum('active','banned','pending') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `ws_transfer`
--

CREATE TABLE `ws_transfer` (
  `transfer_id` int(11) NOT NULL,
  `stk` varchar(255) NOT NULL,
  `chutaikhoan` varchar(255) NOT NULL,
  `nganhang` varchar(255) NOT NULL,
  `logo` varchar(255) NOT NULL,
  `callback` text DEFAULT NULL,
  `kieubank` enum('tudong','thucong') NOT NULL DEFAULT 'thucong'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Đang đổ dữ liệu cho bảng `ws_transfer`
--

INSERT INTO `ws_transfer` (`transfer_id`, `stk`, `chutaikhoan`, `nganhang`, `logo`, `callback`, `kieubank`) VALUES
(1, '*********', 'TRUONG DUC THANH', 'VIB', 'https://api.vieqr.com/icons/VIB.png', '', 'thucong');

--
-- Chỉ mục cho các bảng đã đổ
--

--
-- Chỉ mục cho bảng `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `idx_username` (`username`);

--
-- Chỉ mục cho bảng `ws_dsgoi`
--
ALTER TABLE `ws_dsgoi`
  ADD PRIMARY KEY (`dsgoi_id`);

--
-- Chỉ mục cho bảng `ws_freebill`
--
ALTER TABLE `ws_freebill`
  ADD PRIMARY KEY (`freebill_id`);

--
-- Chỉ mục cho bảng `ws_history_bank`
--
ALTER TABLE `ws_history_bank`
  ADD PRIMARY KEY (`bank_id`);

--
-- Chỉ mục cho bảng `ws_history_card`
--
ALTER TABLE `ws_history_card`
  ADD PRIMARY KEY (`card_id`),
  ADD KEY `taikhoan` (`taikhoan`);

--
-- Chỉ mục cho bảng `ws_history_fakebill`
--
ALTER TABLE `ws_history_fakebill`
  ADD PRIMARY KEY (`fakebill_id`);

--
-- Chỉ mục cho bảng `ws_history_products`
--
ALTER TABLE `ws_history_products`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `fk_taikhoan` (`taikhoan`);

--
-- Chỉ mục cho bảng `ws_limitbill`
--
ALTER TABLE `ws_limitbill`
  ADD PRIMARY KEY (`limitbill_id`),
  ADD KEY `fk_taikhoan` (`taikhoan`);

--
-- Chỉ mục cho bảng `ws_logs`
--
ALTER TABLE `ws_logs`
  ADD PRIMARY KEY (`log_id`);

--
-- Chỉ mục cho bảng `ws_newfeeds`
--
ALTER TABLE `ws_newfeeds`
  ADD PRIMARY KEY (`newfeeds_id`);

--
-- Chỉ mục cho bảng `ws_otpmailler`
--
ALTER TABLE `ws_otpmailler`
  ADD PRIMARY KEY (`id`),
  ADD KEY `username` (`username`);

--
-- Chỉ mục cho bảng `ws_plans`
--
ALTER TABLE `ws_plans`
  ADD PRIMARY KEY (`plans_id`);

--
-- Chỉ mục cho bảng `ws_products`
--
ALTER TABLE `ws_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_id` (`id`);

--
-- Chỉ mục cho bảng `ws_ref`
--
ALTER TABLE `ws_ref`
  ADD PRIMARY KEY (`ref_id`),
  ADD KEY `nguoigioithieu` (`nguoigioithieu`),
  ADD KEY `nguoiduocgioithieu` (`nguoiduocgioithieu`);

--
-- Chỉ mục cho bảng `ws_rsakey`
--
ALTER TABLE `ws_rsakey`
  ADD PRIMARY KEY (`rsa_id`);

--
-- Chỉ mục cho bảng `ws_scaninfo`
--
ALTER TABLE `ws_scaninfo`
  ADD PRIMARY KEY (`scaninfo_id`),
  ADD KEY `username` (`username`);

--
-- Chỉ mục cho bảng `ws_settings`
--
ALTER TABLE `ws_settings`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `ws_spamsms`
--
ALTER TABLE `ws_spamsms`
  ADD PRIMARY KEY (`spamsms_id`);

--
-- Chỉ mục cho bảng `ws_subdomain`
--
ALTER TABLE `ws_subdomain`
  ADD PRIMARY KEY (`domain_id`),
  ADD KEY `username` (`username`);

--
-- Chỉ mục cho bảng `ws_transfer`
--
ALTER TABLE `ws_transfer`
  ADD PRIMARY KEY (`transfer_id`);

--
-- AUTO_INCREMENT cho các bảng đã đổ
--

--
-- AUTO_INCREMENT cho bảng `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT cho bảng `ws_dsgoi`
--
ALTER TABLE `ws_dsgoi`
  MODIFY `dsgoi_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_freebill`
--
ALTER TABLE `ws_freebill`
  MODIFY `freebill_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT cho bảng `ws_history_bank`
--
ALTER TABLE `ws_history_bank`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_history_card`
--
ALTER TABLE `ws_history_card`
  MODIFY `card_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_history_fakebill`
--
ALTER TABLE `ws_history_fakebill`
  MODIFY `fakebill_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT cho bảng `ws_history_products`
--
ALTER TABLE `ws_history_products`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_limitbill`
--
ALTER TABLE `ws_limitbill`
  MODIFY `limitbill_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_logs`
--
ALTER TABLE `ws_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT cho bảng `ws_newfeeds`
--
ALTER TABLE `ws_newfeeds`
  MODIFY `newfeeds_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT cho bảng `ws_otpmailler`
--
ALTER TABLE `ws_otpmailler`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT cho bảng `ws_plans`
--
ALTER TABLE `ws_plans`
  MODIFY `plans_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_products`
--
ALTER TABLE `ws_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_ref`
--
ALTER TABLE `ws_ref`
  MODIFY `ref_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_rsakey`
--
ALTER TABLE `ws_rsakey`
  MODIFY `rsa_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT cho bảng `ws_scaninfo`
--
ALTER TABLE `ws_scaninfo`
  MODIFY `scaninfo_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_settings`
--
ALTER TABLE `ws_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT cho bảng `ws_spamsms`
--
ALTER TABLE `ws_spamsms`
  MODIFY `spamsms_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_subdomain`
--
ALTER TABLE `ws_subdomain`
  MODIFY `domain_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `ws_transfer`
--
ALTER TABLE `ws_transfer`
  MODIFY `transfer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- Các ràng buộc cho các bảng đã đổ
--

--
-- Các ràng buộc cho bảng `ws_history_card`
--
ALTER TABLE `ws_history_card`
  ADD CONSTRAINT `ws_history_card_ibfk_1` FOREIGN KEY (`taikhoan`) REFERENCES `users` (`username`);

--
-- Các ràng buộc cho bảng `ws_limitbill`
--
ALTER TABLE `ws_limitbill`
  ADD CONSTRAINT `fk_taikhoan` FOREIGN KEY (`taikhoan`) REFERENCES `users` (`username`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Các ràng buộc cho bảng `ws_ref`
--
ALTER TABLE `ws_ref`
  ADD CONSTRAINT `ws_ref_ibfk_1` FOREIGN KEY (`nguoigioithieu`) REFERENCES `users` (`username`),
  ADD CONSTRAINT `ws_ref_ibfk_2` FOREIGN KEY (`nguoiduocgioithieu`) REFERENCES `users` (`username`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
