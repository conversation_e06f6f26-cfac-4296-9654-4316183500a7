<?php if (array_filter(['/dang-ky', '/dang-nhap', '/login','/signIn','/register','/signUp'], fn($keyword) => strpos($current_url, $keyword)==0)): ?>
<data id="gt" value="<?=$TD->Setting('id-geetest')?>"></data>
<data id="public-key" value="<?=$wtSecurity->publicKey;?>"></data>
<?php endif; ?>
<script src="/<?=__LIBRARY__?>/jquery/jquery-3.7.1.min.js"></script>
<script src="/<?=__LIBRARY__?>/jquery.pjax/jquery.pjax.min.js"></script>
<script src="/<?=__LIBRARY__?>/toast@1.0.1/fuiToast.min.js"></script>
<script src="/<?=__LIBRARY__?>/toast-cute/cute-alert.js"></script>
<script src="/<?=__LIBRARY__?>/jsencrypt/jsencrypt.min.js"></script>
<script src="/<?=__LIBRARY__?>/sweetalert@2.1.2/sweetalert.min.js"></script>
<script src="//cdn.plyr.io/3.7.8/plyr.js"></script>
<script src="/<?=__LIBRARY__?>/fancybox/fancybox.umd.js"></script>
<script src="/<?=__LIBRARY__?>/lazysizes/lazysizes.min.js" async></script>
<script src="/<?=__JS__?>/wt-customize.min.js?v=<?=$TD->Setting('cache')?><?=rand(111111,2222222222);?>"></script>
<?php if (strpos($current_url, '/dang-ky') !== false): ?>
<script src="/<?=__LIBRARY__?>/captcha/gt4.js"></script>
<script src="/<?=__LIBRARY__?>/captcha/gt4-vh.js"></script>
<?php endif; ?>
<script src="/<?=__JS__?>/wt-upload.min.js?v=<?=$TD->Setting('cache')?>.352"></script>
<script>
// toastr fallback: some bundles expect window.toastr; map to FuiToast if not present
window.toastr = window.toastr || {
	error: function(msg){ if (window.FuiToast && FuiToast.error) FuiToast.error(msg); },
	info: function(msg){ if (window.FuiToast && FuiToast.info) FuiToast.info(msg); },
	success: function(msg){ if (window.FuiToast && FuiToast.success) FuiToast.success(msg); },
	warning: function(msg){ if (window.FuiToast && (FuiToast.warning||FuiToast.info)) (FuiToast.warning||FuiToast.info)(msg); },
	options: {}
};

// Ẩn loading indicator sau khi trang load xong
document.addEventListener('DOMContentLoaded', function() {
    const loadingIndicator = document.querySelector('.lavender-loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
});

// Backup: ẩn loading sau 3 giây
setTimeout(function() {
    const loadingIndicator = document.querySelector('.lavender-loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
}, 3000);
</script>
<script src="/<?=__JS__?>/main.min.js?v=<?=rand(111111,2222222222);?>"></script>
</body>
</html>