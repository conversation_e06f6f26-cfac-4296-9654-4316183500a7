@import url('https://fonts.googleapis.com/css?family=Potta One|Braah+One&display=swap');
@import url('https://fonts.googleapis.com/css?family=Audiowide|Sriracha');
@import url('https://fonts.googleapis.com/css?family=Pattaya|Righteous|Saira&display=swap');
@import url('https://fonts.googleapis.com/css?family=Rowdies|Monomaniac+One|ZCOOL+QingKe+HuangYou|Concert One|Nunito&display=swap');
* {
	-webkit-user-select: none; 
	-ms-user-select: none;
	user-select: none;
}
/* ::-webkit-scrollbar {
	width: 5px;
	background-color: #F5F5F5;

  }
  ::-webkit-scrollbar-thumb{background-color:#612351;background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.2) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.2) 50%,rgba(255,255,255,.2) 75%,transparent 75%,transparent)}
  ::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #F5F5F5;
  } */
body.dark-mode .note-editable {
    color: white;
}

body.dark-mode .js-preloader {
  background:#101924!important;
}
body:not(.dark-mode) .nk-main {
  position: relative;
  background-image: url('../img/bg-light.jpg') !important;
  background-size: cover;
  background-position: center;
  height: 100vh;
}
body:not(.dark-mode) .nk-main::after {
  content: ""; 
  position: absolute;
  top: 0; 
  left: 0; 
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.836);
  z-index: 1; 
}
body:not(.dark-mode) .nk-main > * {
  position: relative;
  z-index: 2;
}
* ::-webkit-scrollbar-thumb {
    background-color: #6576ff;
    border-radius: 6249.9375rem
}
* ::-webkit-scrollbar {
  width: .225rem;
  height: .125rem
}
.td-loader {
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: ***********999999;
    height: 100%;
    background-color: rgba(218, 218, 218, 0.082);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .td-loader img {
    width: 32px!important;
    height: 32px!important;
  }
  .ws-avatar-1 {
	object-fit: cover;
	height: 100%!important;
	width: 100% !important;
  }
  .nk-sticky-toolbar li a{
	height: 33px !important;
    width: 33px!important;
  }

  @media (min-width: 576px) {
    .nk-sticky-toolbar {
        top:50% !important;
    }
    .nk-sticky-toolbar li a {
        font-size: 20px;
		height: 33px !important;
		width: 33px!important;
    }
}
.modal.fade .modal-dialog {
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: scale(0.9);
    opacity: 0;
}

.modal.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

.modal.fadeout .modal-dialog {
    animation: fadeOut 0.3s ease, scaleOut 0.3s ease;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes scaleOut {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(0.9);
    }
}

.dropzone {
    text-align: center;
}

.dz-message {
    font-size: 14px;
    color: #888;
}

#td-uploads {
    display: none;
}
#image-preview {
    position: relative;
    justify-content: flex-start;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
}
#image-preview img {
    width: 180px !important;
    height: 180px !important;
}

.preview-image {
    flex: 1 1 100px; 
    max-width: 100px; 
    max-height: 100px;
    margin-right: 10px;
    margin-bottom: 10px;
    object-fit: cover;
    border: 2px dashed #ccc;
    border-radius: 5px;
    position: relative; 
} 
.delete-image {
    position: absolute;
    transform: translate(-95%, -50%);
    background-color: rgb(161 161 161 / 50%);
    border: none;
    border-radius: 50%;
    width: 25px; 
    height: 25px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
.level-icon {
    height: 18px !important;
    width: auto !important;
}
.display-name {
    background-image: -webkit-linear-gradient(90deg, #07c160, #fa0000 25%, #9e00fa 50%, #fb6bea 75%, #00eeff);
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    background-size: 100% 300%;
    font-weight: 800;
    animation: vtd 2s linear infinite;
}

@keyframes vtd {
    0% {
        background-position: 0 0;
    }
    50% {
        background-position: 0 -150%;
    }
    100% {
        background-position: 0 0;
    }
}
@keyframes cycle-colors {
    0% { border-color: hsl(0, 100%, 50%); }
    25% { border-color: hsl(90, 100%, 50%); }
    50% { border-color: hsl(180, 100%, 50%); }
    75% { border-color: hsl(270, 100%, 50%); }
    100% { border-color: hsl(360, 100%, 50%); }
  }
  
  @keyframes pulse {
    to {
      opacity: 0;
      transform: scale(1);
    }
  }
  
  .avatar::before,
  .avatar::after {
    animation: pulse 2s linear infinite;
    border: #fff solid 8px;
    border-radius: 9999px;
    box-sizing: border-box;
    content: ' ';
    height: 140%;
    left: -20%;
    opacity: .6;
    position: absolute;
    top: -20%;
    transform: scale(0.714);
    width: 140%;
    z-index: 1;
  }
  
  .avatar::after {
    animation-delay: 1s;
  }
  .avatar:hover::before,
  .avatar:hover::after {
    animation: pulse 1s linear infinite, cycle-colors 6s linear infinite;
  }
  
  .avatar:hover::after {
    animation-delay: .5s;
  }
  .spinner {
    display: inline-block;
    animation: spinner 1s linear infinite;
  }
  @keyframes spinner {
    to {transform: rotate(360deg);}
  }
  .bg-pink {
    border-color: #ff00e5;
    background: #ff00e5
}
.hided {
  display: none;
}
.user-avatar {
  cursor: pointer;
}
.site-logo img {
max-height: 80px !important;
}
.api-bank {
  display: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  white-space: normal !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important; 
}
.bg-anime {
  min-height: 150px;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  position: relative;
  border-radius: 10px !important;
}
.bg-anime::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.377);
  backdrop-filter: blur(5px);
  z-index: 1;
  border-radius: 10px !important;
}
.bg-anime * {
  position: relative;
  z-index: 2;
}
.bg-newfeeds {
  background-image: url('../img/anime/1.webp') !important;
}
.bg-user {
  background-image: url('../img/anime/2.webp') !important;
}
.bg-bank {
  background-image: url('../img/anime/3.webp') !important;
}
.bg-vip {
  background-image: url('../img/anime/4.jpg') !important;
}
.center-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100%; 
}
.text-boldz {
  font-weight: 700;
  text-shadow: -1px 6px 9px rgba(0,0,0,1);
}
.border-radius-10 {
  border-radius: 10px !important;
}
.check-rotate-support {
  background: black;
  min-height: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: ***********;
  pointer-events: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  overflow: hidden!important;
} 

@media screen and (min-width: 769px) {
  .check-rotate-support {
    height:100vh;
    width: 100vw;
  }
}

.check-rotate-support::before {
  content: '';
  display: block;
  background-image: url('../img/rotate.png');
  height: 220px;
  width: 150px;
  background-size: cover;
  margin-left: -20px;
  animation: rotate-phone 1.7s ease-in-out infinite alternate;
  transform: rotate(90deg);
}

.check-rotate-support::after {
  content: 'Vui lòng xoay ngang điện thoại';
  color: white;
  font-size: 1.2em;
  margin-top: 30px;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
}


@keyframes rotate-phone {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-90deg);
  }
  100% {
    transform: rotate(-90deg);
  }
}
@media only screen and (orientation: landscape) {
  .check-rotate-support {
    display: none;
  }
}

@media only screen and (orientation: portrait) {
  .check-rotate-support {
    display: flex;
  }
}
.ml-5 {
  margin-left: 5px;
}
.mt1 {
  margin-top: 1px !important;
}
.mt2 {
  margin-top: 2px !important;
}
.icon-newfeed img {
  width: 18px;
  }