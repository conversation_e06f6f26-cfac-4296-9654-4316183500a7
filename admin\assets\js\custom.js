function Administrator(t){for(var n=$("select[default]"),e=0;e<n.length;e++)$(n[e]).val($(n[e]).attr("default")||0);$("html, body").animate({scrollTop:0},"slow");var s,i={};i.ws=$("body"),i.ajax="/ajax/global/admin","undefined"!=typeof initGeetest4&&initGeetest4({captchaId:"",product:"float",riskType:"slide"},(function(t){captchaObj=t,captchaObj.appendTo("#captcha_login"),s&&s()})),$('[data-toggle="tooltip"]').length>0&&($('[data-toggle="tooltip"]').tooltip(),a,Fancybox.bind("[data-fancybox]",{}));function o(t){FuiToast.promise(t,{loading:"Vui lòng đợi...",success:t=>(Wsloader(!1),t),error:t=>(Wsloader(!1),t)},{isClose:!0})}function c(t,n,e){return function(a){a.preventDefault(),$(t).show(),$(`a[href="${e}"]`).removeClass("active"),$(this).addClass("active"),$(".nk-menu-item").removeClass("active current-page"),$(this).closest(".nk-menu-item").addClass("active current-page"),$(n).addClass("active")}}$("[data-fancybox]").length>0&&Fancybox.bind("[data-fancybox]",{}),i.ws.append($('<div class="td-loader"style="display:none;"><img src="/public/src/vtd/img/icon/loader.gif"></div>')),$('input[type="search"]').each((function(){"DataTables_Table_1"===$(this).attr("aria-controls")&&$(this).attr("placeholder","Nhập để tìm kiếm...")})),window.Wsloader=function(t){t?$(".td-loader").show():$(".td-loader").hide()},"/admin/"===window.location.pathname&&$(".nk-menu-item.home").addClass("active current-page"),window.location.pathname.includes("/md5/")&&$(".nk-menu-item.report").addClass("active current-page"),"/admin/"===window.location.pathname&&window.location.replace("/admin/dashboard"),i.ws.on("click",'a[href="#show-newfeeds"]',c(".td-newfeeds",".nk-menu-item.newfeeds","#show-newfeeds")),i.ws.on("click","li.newfeeds > a.nk-menu-toggle",(function(t){t.preventDefault(),$(this).closest("li.newfeeds").hasClass("active current-page")&&$('a[href="#show-newfeeds"]').closest("li.nk-menu-item").addClass("active current-page")})),i.ws.on("click",'a[href="#show-product"]',c(".td-product-page",".nk-menu-item.product-page","#show-product")),$("li.product-page").hasClass("active current-page")&&$('a[href="#show-product"]').closest("li.nk-menu-item").addClass("active current-page"),i.ws.on("click",'a[href="#show-users"]',c(".td-users",".nk-menu-item.users","#show-users")),$("li.users").hasClass("active current-page")&&$('a[href="#show-users"]').closest("li.nk-menu-item").addClass("active current-page"),i.ws.on("click",'a[href="#show-bank"]',c(".td-bank",".nk-menu-item.bank","#show-bank")),$("li.bank").hasClass("active current-page")&&$('a[href="#show-bank"]').closest("li.nk-menu-item").addClass("active current-page"),i.ws.on("click",".show-pw",(function(){var t=$(this).closest(".input-group").find("input"),n=$(this).find("i");"password"===t.attr("type")?(t.attr("type","text"),n.removeClass("ri-eye-line").addClass("ri-eye-off-line")):(t.attr("type","password"),n.removeClass("ri-eye-off-line").addClass("ri-eye-line"))})),requestAnimationFrame((function t(){$(".time-ago").each((function(){$(this).text(function(t){var n=Math.floor((new Date-new Date(t))/1e3),e=Math.floor(n/60),a=Math.floor(e/60),s=Math.floor(a/24),i=Math.floor(s/7),o=Math.floor(s/30),r=Math.floor(s/365);return r>0?r+" năm trước":o>0?o+" tháng trước":i>0?i+" tuần trước":s>0?s+" ngày trước":a>0?a+" giờ trước":e>0?e+" phút trước":"vừa xong"}($(this).data("timeago")))})),requestAnimationFrame(t)})),$(".nk-footer-copyright.display-name").html('&copy; 2021 Developed by <a href="https://thanhdieu.com" target="_blank">ThanhDieu 💗</a>');var l=function(t){var n=null;if(document.cookie&&""!==document.cookie)for(var e=document.cookie.split(";"),a=0;a<e.length;a++){var s=e[a].trim();if(s.substring(0,t.length+1)===t+"="){n=decodeURIComponent(s.substring(t.length+1));break}}return n}("theme");function u(t,n,e){var a="";if(e){var s=new Date;s.setTime(s.getTime()+24*e*60*60*1e3),a="; expires="+s.toUTCString()}document.cookie=t+"="+(n||"")+a+"; path=/"}l||(u("theme","dark",365),l="dark"),"dark"===l?(i.ws.addClass("dark-mode"),$(".toggle-theme").addClass("dark-mode"),$(".toggle-theme em").removeClass("ni-sun").addClass("ni-moon")):(i.ws.removeClass("dark-mode"),$(".toggle-theme").removeClass("dark-mode"),$(".toggle-theme em").removeClass("ni-moon").addClass("ni-sun")),$(".ws-toggle-theme").click((function(t){t.preventDefault(),i.ws.hasClass("dark-mode")?(i.ws.removeClass("dark-mode"),$(".toggle-theme").removeClass("dark-mode"),$(".toggle-theme em").removeClass("ni-moon").addClass("ni-sun"),u("theme","light",365)):(i.ws.addClass("dark-mode"),$(".toggle-theme").addClass("dark-mode"),$(".toggle-theme em").removeClass("ni-sun").addClass("ni-moon"),u("theme","dark",365))})),i.ws.hasClass("dark-mode")?$(".toggle-theme em").removeClass("ni-sun").addClass("ni-moon"):$(".toggle-theme em").removeClass("ni-moon").addClass("ni-sun"),$(".link-list-menu li a").each((function(){window.location.pathname.split("/").pop()===$(this).attr("href").split("/").pop()&&$(this).addClass("active")})),i.ws.on("change",'select[name="kieubank"]',(function(){var t=$(this).val();"tudong"===t?$(".api-bank").slideDown():"thucong"===t&&$(".api-bank").slideUp()})),i.ws.on("click",".dl-img",(function(t){t.preventDefault();var n=$(this).data("download"),e=$(this).data("bill-id"),a=$(this).data("name-site"),s=document.createElement("a");s.href=n,s.download=a+"-"+e,document.body.appendChild(s),s.click(),document.body.removeChild(s)})),i.ws.on("click",".td-copy",(function(){var t=$(this).data("copy");navigator.clipboard.writeText(t).then((()=>{t.length>35?FuiToast.success("Đã sao chép: "+t.substring(0,35)+"..."):FuiToast.success("Đã sao chép: "+t)})).catch((t=>{FuiToast.error("Lỗi sao chép: "+t.message)}))})),i.ws.on("input",'.ws-sotien,input[name="sotien"]',(function(){var t=$(this).val().replace("đ","").replace(/\D/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,".");t.length>0&&(t+="đ"),$(this).val(t)})),i.ws.on("input",".ws-percent",(function(){let t=$(this).val().replace("%","");t=parseFloat(t),isNaN(t)||t<0?t=0:t>100&&(t=100),$(this).val(t+"%")})),$(".ws-percent").on("focus",(function(){$(this).val($(this).val().replace("%",""))})),$(".ws-percent").on("blur",(function(){let t=$(this).val().replace("%","");t=parseFloat(t),isNaN(t)?$(this).val("0%"):$(this).val(t+"%")})),$("#slug").on("input",(function(){var t=$(this).val().toLowerCase().replace(/\s+/g,"-");t=t.replace(/[^a-z0-9-]/g,""),$(this).val(t)})),$("#select-image").click((function(){$("#td-uploads").click()})),$("#td-uploads").change((function(){var t=$(this)[0].files;m(t)||f(t)}));var d=document.getElementById("custom-dropzone");function m(t){for(var n=0;n<t.length;n++){if(-1===["image/gif","image/jpeg","image/jpg","image/png","image/webp"].indexOf(t[n].type))return Swal.fire("Thất Bại","Định dạng ảnh không được hỗ trợ!","error"),!0;if(t[n].size>2097152)return Swal.fire("Thất Bại","Kích thước ảnh không được vượt quá 2MB!","error"),!0}return!1}d&&(d.addEventListener("dragover",(function(t){t.preventDefault(),$(this).addClass("dragover")})),d.addEventListener("dragleave",(function(t){t.preventDefault(),$(this).removeClass("dragover")})),d.addEventListener("drop",(function(t){t.preventDefault(),$(this).removeClass("dragover");var n=t.dataTransfer.files;m(n)||f(n)})));var h=[];function f(t){$(".td-message").hide();for(var n=document.getElementById("td-uploads").hasAttribute("multiple")?t.length:1,e=0;e<n;e++)!function(t){var n=new FileReader;n.onload=function(n){var e=document.createElement("div");e.className="image-container";var a=document.createElement("img");a.src=n.target.result,a.className="preview-image";var s=document.createElement("button");s.innerHTML='<em class="icon ni ni-cross"></em>',s.className="delete-image",s.onclick=function(){e.remove(),0===$("#image-preview .image-container").length&&$(".td-message").show();var n=h.indexOf(t);-1!==n&&h.splice(n,1)},e.appendChild(a),e.appendChild(s),$("#image-preview").append(e),h.push(t)},n.readAsDataURL(t)}(t[e])}if($("#limit-account").length)(g=document.getElementById("limit-account")).noUiSlider.on("update",(function(t,n){var e=Math.round(t[n]);$("span[data-slider-count]").text(e)}));else if($("#auto-delete").length){var g;(g=document.getElementById("auto-delete")).noUiSlider.on("update",(function(t,n){var e=Math.round(t[n]);$("span[data-slider-count]").text(e+" ngày")}))}$("#is-modal").change((function(){var t=$(this).next(".custom-control-label");$(this).prop("checked")?t.removeClass("text-danger").addClass("text-primary").text("Kích Hoạt"):t.removeClass("text-primary").addClass("text-danger").text("Vô Hiệu Hoá")})),i.ws.on("click",".user-avatar",(function(){var t=$(this).data("input");$("#"+t).click()})),i.ws.on("change",'input[type="file"]',(function(t){var n=t.target,e=$(n).siblings(".user-avatar").data("preview");if(n.files&&n.files[0]){var a=new FileReader;a.onload=function(t){$("#"+e).attr("src",t.target.result)},a.readAsDataURL(n.files[0])}})),i.ws.on("submit",".auth-admin-lg",(function(t){!function(t,n){if(t.preventDefault(),captchaObj&&"function"==typeof captchaObj.getValidate){if(!captchaObj.getValidate())return FuiToast.error("Vui lòng xác minh captcha!");"function"==typeof n?n(t):FuiToast.error("No custom submit handler provided!")}else FuiToast.error("Captcha object is not properly initialized!")}(t,(function(t){var n=$(t.target).serialize();o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(t){t.success?i.ws.fadeOut(200,(function(){history.go(0)})).fadeIn(200):(captchaObj.reset(),e(t.message))},error:function(t,n,e){FuiToast.error("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))}))})),i.ws.on("click",".user-logout",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn đăng xuất khỏi thông tin đăng nhập hiện tại của mình không?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, đăng xuất!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{user_logout:!0},dataType:"json",success:function(e){e.success?(t(e.message),i.ws.fadeOut(200,(function(){history.go(0)})).fadeIn(200)):n(e.message)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("submit",".ws-settings",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this).serialize();o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".reset-key-aes",(function(t){t.preventDefault(),Swal.fire({title:"Thông Báo",text:"Tất cả thành viên sẽ bị đăng xuất kể cả bạn, bạn vẫn muốn tiếp tục thực hiện điều này?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, reset ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{reset_key_aes:!0},dataType:"json",success:function(e){200==e.status?t(e.msg):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".clear-cache",(function(t){t.preventDefault(),Swal.fire({title:"Thông Báo",text:"Chức năng này dùng để làm mới lại dữ liệu cache như JavaScript, CSS v.v.., khi bạn update code thì bạn mới cần bật chức năng này, có thể bị overload nếu máy chủ yếu.",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Làm mới ngay!"}).then((t=>{if(t.isConfirmed)return FuiToast.error("Không khả dụng!")}))})),$("#is-modal").change((function(){Wsloader(!0);var t=$(this).is(":checked")?1:0;o(new Promise((function(n,e){$.ajax({type:"post",url:i.ajax,data:{modal:t},dataType:"json",success:function(t){200==t.status?n(t.msg):e(t.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".ws-setting-info",(function(t){t.preventDefault(),Wsloader(!0);var n=new FormData(this);o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,processData:!1,contentType:!1,data:n,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),$("#ws-settings-security").length&&($("#sensitivity-anti-spam").change((function(){Wsloader(!0);var t=$(this).is(":checked")?1:0;o(new Promise((function(n,e){$.ajax({type:"post",url:i.ajax,data:{anti_spam:t},dataType:"json",success:function(t){200==t.status?n(t.msg):e(t.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),$("#admin-page").change((function(){Wsloader(!0);var t=$(this).is(":checked")?1:0;o(new Promise((function(n,e){$.ajax({type:"post",url:i.ajax,data:{AdminPage:t},dataType:"json",success:function(t){200==t.status?n(t.msg):e(t.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),$("#is-captcha").change((function(){Wsloader(!0);var t=$(this).is(":checked")?1:0;o(new Promise((function(n,e){$.ajax({type:"post",url:i.ajax,data:{isCaptcha:t},dataType:"json",success:function(t){200==t.status?n(t.msg):e(t.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click","#status-maintenance",(function(t){t.preventDefault(),Wsloader(!0);var n=1==$(this).data("status")?0:1;o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:{maintenance:n},dataType:"json",success:function(a){200==a.status?(t(a.msg),$("#status-maintenance").data("status",n),$("#status-maintenance").text(1==n?"Đang Bật":"Đang Tắt"),$("#status-maintenance").removeClass("btn-danger btn-primary").addClass(1==n?"btn-primary":"btn-danger"),$(".ws-baotri .badge").removeClass("bg-danger bg-success").addClass(1==n?"bg-success":"bg-danger").text(1==n?"Đang Bảo Trì":"Đã Tắt Bảo Trì")):e(a.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click","#status-https",(function(t){t.preventDefault(),Wsloader(!0);var n=1==$(this).data("status")?0:1;o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:{https:n},dataType:"json",success:function(a){200==a.status?(t(a.msg),$("#status-https").data("status",n),$("#status-https").text(1==n?"Đang Bật":"Đang Tắt"),$("#status-https").removeClass("btn-danger btn-primary").addClass(1==n?"btn-primary":"btn-danger")):e(a.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),g.noUiSlider.on("change",(function(t,n){Wsloader(!0),document.getElementById("limit-account").noUiSlider.on("update",(function(t,n){var e=Math.round(t[n]);$("span[data-slider-count]").text(e)}));var e=Math.round(t[n]);o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{limit_account:e},dataType:"json",success:function(e){200==e.status?t(e.msg):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))}))),i.ws.on("submit","#Cong-Tien",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this).serialize();$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(t){Wsloader(!1),200==t.status?($("#cong-tien").modal("hide"),$("input[name='sotien']").val(""),$(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),Swal.fire("Thông Báo",t.msg,"success")):Swal.fire("Thất Bại",t.msg,"error")},error:function(t,n,e){Wsloader(!1),Swal.fire("Mất Kết Nối","Mất kết nối đến máy chủ vui lòng kiểm tra lại! vui lòng kiểm tra lại!","error")}})})),i.ws.on("submit","#Tru-Tien",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this).serialize();$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(t){Wsloader(!1),200==t.status?($("#tru-tien").modal("hide"),$("input[name='sotien']").val(""),$(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),Swal.fire("Thông Báo",t.msg,"success")):Swal.fire("Thất Bại",t.msg,"error")},error:function(t,n,e){Wsloader(!1),Swal.fire("Mất Kết Nối","Mất kết nối đến máy chủ vui lòng kiểm tra lại! vui lòng kiểm tra lại!","error")}})})),i.ws.on("submit","#editUsers",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this).serialize();$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(t){Wsloader(!1),200==t.status?($(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),Swal.fire("Thông Báo",t.msg,"success")):Swal.fire("Thất Bại",t.msg,"error")},error:function(t,n,e){Wsloader(!1),Swal.fire("Mất Kết Nối","Mất kết nối đến máy chủ vui lòng kiểm tra lại! vui lòng kiểm tra lại!","error")}})})),i.ws.on("click","#banned-users",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this),e=n.data("user-id");o(new Promise((function(t,a){$.post(i.ajax,{user_id:e,banned_users:!0},(function(e){if(e.status){var s={1:{text:"Mở Khoá Tài Khoản",class:"bg-danger",statusText:"Đình Chỉ"},0:{text:"Khoá Tài Khoản",class:"bg-success",statusText:"Hoạt Động"}};if(e.new_status in s){var i=s[e.new_status];n.find("span").text(i.text),n.closest("tr").find(".tb-status").removeClass("bg-success bg-danger").addClass(i.class).text(i.statusText),1==e.new_status?t("Đã đình chỉ tài khoản này!"):t("Đã mở khoá tài khoản!")}else a(e.msg)}}),"json").fail((function(){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}))})))})),i.ws.on("click",".truncate-logs",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xóa tất cả nhật ký, dữ liệu liên quan tới bảng logs sẽ không còn tồn tại nữa?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{truncate_logs:!0},dataType:"json",success:function(e){200==e.status?($(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".truncate-transfer",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xoá tất cả ngân hàng hiện có trong bảng?, bạn phải thiết lập lại ngân hàng mới sau khi xoá tất cả!",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{truncate_transfer:!0},dataType:"json",success:function(e){200==e.status?($("table tbody").empty(),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("submit",".add-transfer-bank",(function(t){t.preventDefault();var n=$(this).serialize();Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(n){200==n.status?($("form.add-transfer-bank")[0].reset(),$("#addTransfer").modal("hide"),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".edit-transfer-bank",(function(t){t.preventDefault();var n=$(this).serialize();Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(n){200==n.status?($(".modal").modal("hide"),i.ws.fadeOut(200,(function(){history.go(0)})).fadeIn(2020),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".delete-transfer",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xoá ngân hàng này?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{if(t.isConfirmed){Wsloader(!0);var n=$(this).data("transfer-id"),e=$(this);o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{delete_transfer:!0,transfer_id:n},dataType:"json",success:function(n){200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(t,n,e){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))}}))})),i.ws.on("submit",".ws-setting-callback",(function(t){t.preventDefault();var n=$(this).serialize();Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".ws-create-newfeeds",(function(t){t.preventDefault(),Wsloader(!0);var n=new FormData(this);$.ajax({type:"post",url:i.ajax,data:n,dataType:"json",processData:!1,contentType:!1,success:function(t){Wsloader(!1),200==t.status?Swal.fire({title:"<strong>Thông Báo</strong>",icon:"success",html:`${t.msg}`,showCancelButton:!1,allowOutsideClick:!1,confirmButtonText:"Đồng ý",onClose:()=>{location.reload()}}):Swal.fire("Thất Bại",t.msg,"error")},error:function(t,n,e){Wsloader(!1),Swal.fire("Mất Kết Nối","Mất kết nối đến máy chủ vui lòng kiểm tra lại!, vui lòng kiểm tra lại!","error")}})})),i.ws.on("submit",".re-edit-newfeeds",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".modal-popup",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){n.success?t(n.message):e(n.message)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".delete-newfeeds",(function(t){t.preventDefault();var n=$(this).data("newfeeds-id"),e=$(this);Swal.fire({title:"Cảnh Báo",text:"Bạn có chắn chắc xoá bảng tin này không?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{delete_newfeeds:!0,id_newfeeds:n},dataType:"json",success:function(n){200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(t,n,e){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click","#delete-account",(function(t){t.preventDefault();var n=$(this).data("username"),e=$(this);Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xóa thành viên này, mọi dữ liệu liên quan đến thành viên này sẽ bị xoá!",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{delete_users:!0,username:n},dataType:"json",success:function(n){200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(t,n,e){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".hide-newfeeds",(function(t){t.preventDefault();var n=$(this).data("newfeeds-id"),e=$(this);Wsloader(!0),o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{hide_newfeeds:!0,id_newfeeds:n},dataType:"json",success:function(n){if(Wsloader(!1),200==n.status){var s=e.closest("tr").find(".badge");"1"==n.new_status?(e.find("span").text("Ẩn Bảng Tin"),s.removeClass("bg-outline-danger").addClass("bg-outline-success").text("Hoạt Động")):(e.find("span").text("Hiện Bảng Tin"),s.removeClass("bg-outline-success").addClass("bg-outline-danger").text("Đang Ẩn")),t(n.msg)}else a(n.msg)},error:function(t,n,e){Wsloader(!1),a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click","#fuck-devtools",(function(t){t.preventDefault(),Wsloader(!0);var n=1==$(this).data("status")?0:1;o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:{fuckdevtools:n},dataType:"json",success:function(a){200==a.status?(t(a.msg),$("#fuck-devtools").data("status",n),$("#fuck-devtools").text(1==n?"Đang Bật":"Đang Tắt"),$("#fuck-devtools").removeClass("btn-danger btn-primary").addClass(1==n?"btn-primary":"btn-danger")):e(a.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit","#TaoThanhVien",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($("form")[0].reset(),$("#addUser").modal("hide"),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".backup-sql",(function(t){t.preventDefault(),Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{backup_sql:!0},dataType:"json",success:function(e){200==e.status?(window.location.href=e.file,t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".create-new-plan",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($("form.create-new-plan")[0].reset(),setTimeout((()=>{i.ws.fadeOut(200,(function(){history.go(0)})).fadeIn(200)}),1300),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".lock-plan",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this),e=n.data("plan-id");o(new Promise((function(t,a){$.post(i.ajax,{goi_id:e,action:"lock-plan"},(function(e){if(200==e.status){var s={0:{text:"Mở Khoá",class:"btn-warning",icon:"ri-lock-unlock-line"},1:{text:"Khoá Gói",class:"btn-danger",icon:"ri-lock-2-line"}}[e.new_status];n.removeClass("btn-danger btn-warning").addClass(s.class).html('<i class="'+s.icon+' me-1"></i>'+s.text),n.closest("tr").find(".tb-status").removeClass("bg-success bg-danger").addClass(s.class).text(s.text),1==e.new_status?t("Đã mở khoá gói này!"):t("Đã khoá gói này!")}else a(e.msg)}),"json").fail((function(){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}))})))})),i.ws.on("click",".truncate-plans",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xóa tất cả các gói, tất cả dữ liệu gói sẽ không còn tồn tại nữa?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{action:"truncate-plans"},dataType:"json",success:function(e){200==e.status?($(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".truncate-history-plans",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xóa tất cả các gói mà thành viên đã mua, sao khi xoá không thể khôi phục bằng cách nào!",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{action:"truncate-history-plans"},dataType:"json",success:function(e){200==e.status?($("table tbody").empty(),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("submit",".re-edit-plan",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($(".modal").modal("hide"),$(".thanhdieu-refresh").load(location.href+" .thanhdieu-refresh > *"),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".set-limit-bill",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($(".modal").modal("hide"),$("form.set-limit-bill")[0].reset(),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".set-time-bill",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($(".modal").modal("hide"),$("form.set-time-bill")[0].reset(),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".truncate-all-bill",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Xoá tất cả lịch sử tạo bill để giải phóng dung lượng, tuy nhiên khách hàng sẽ không còn thấy ảnh tạo bill ở lịch sử tạo, bạn có muốn tiếp tục?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{action:"truncate-all-bill"},dataType:"json",success:function(e){200==e.status?($("table tbody").empty(),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".delete-bill",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xoá bill này?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{if(t.isConfirmed){Wsloader(!0);var n=$(this).data("bill-id"),e=$(this);o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{action:"delete-bill",bill_id:n},dataType:"json",success:function(n){200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(t,n,e){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))}}))})),i.ws.on("click",".banned-plan",(function(t){t.preventDefault(),Wsloader(!0);var n=$(this),e=n.data("plan-id");o(new Promise((function(t,a){$.post(i.ajax,{plan_id:e,action:"banned-plan"},(function(e){if(e.status){var s={1:{text:"Hoạt Động",class:"bg-outline-success",buttonText:"Khoá",icon:'<i class="ri-lock-2-line me-1"></i>'},2:{text:"Tạm Khoá",class:"bg-outline-warning",buttonText:"Mở Khoá",icon:'<i class="ri-lock-unlock-line me-1"></i>'}};if(e.new_status in s){var i=s[e.new_status];n.closest("tr").find(".tb-status").removeClass("bg-outline-success bg-outline-warning bg-outline-danger").addClass(i.class).text(i.text),n.removeClass("bg-secondary bg-warning").addClass(1===e.new_status?"bg-warning":"bg-secondary").html(i.icon+i.buttonText),2==e.new_status?t("Đã tạm khoá gói này!"):t("Đã mở khoá gói này!")}else a(e.msg)}}),"json").fail((function(){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}))})))})),i.ws.on("click",".delete-plan",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Bạn có chắc chắn muốn xoá gói thành viên này?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{if(t.isConfirmed){Wsloader(!0);var n=$(this).data("plan-id"),e=$(this);o(new Promise((function(t,a){$.ajax({type:"post",url:i.ajax,data:{action:"delete-plan",plan_id:n},dataType:"json",success:function(n){200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(t,n,e){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))}}))})),$("#auto-delete").length&&g.noUiSlider.on("change",(function(t,n){Wsloader(!0),document.getElementById("auto-delete").noUiSlider.on("update",(function(t,n){var e=Math.round(t[n]);$("span[data-slider-count]").text(e+" ngày")}));t=Math.round(t[n]);o(new Promise((function(n,e){$.ajax({type:"post",url:i.ajax,data:{action:"auto-delete-bill",values:t},dataType:"json",success:function(t){200==t.status?n(t.msg):e(t.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".set-expiration-all-plan",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"post",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?($(".modal").modal("hide"),$("form.set-expiration-all-plan")[0].reset(),t(n.msg)):e(n.msg)},error:function(t,n,a){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".ws-create-products",(function(t){if(t.preventDefault(),Wsloader(!0),0===h.length)return Wsloader(!1),void Swal.fire("Thất Bại","Vui lòng chọn một hình ảnh để tải lên!","error");var n=new FormData(this);h.forEach((function(t,e){n.append("anhsanpham["+e+"]",t)})),$.ajax({type:"POST",url:i.ajax,data:n,contentType:!1,processData:!1,dataType:"json",success:function(t){Wsloader(!1),200==t.status?($(".ws-create-products")[0].reset(),Swal.fire({title:"<strong>Thông Báo</strong>",icon:"success",html:`${t.msg}`,showCancelButton:!1,allowOutsideClick:!1,confirmButtonText:"Đồng ý",onClose:()=>{location.reload()}})):Swal.fire("Thất Bại",t.msg,"error")},error:function(t,n,e){Wsloader(!1),Swal.fire("Mất Kết Nối","Mất kết nối đến máy chủ vui lòng kiểm tra lại!, vui lòng kiểm tra lại!","error")}})})),i.ws.on("submit",".re-edit-newfeed",(function(t){t.preventDefault();var n=new FormData(this);Wsloader(!0),o(new Promise((function(t,e){$.ajax({type:"POST",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("submit",".re-edit-product",(function(t){t.preventDefault();var n=new FormData(this);o(new Promise((function(t,e){$.ajax({type:"POST",url:i.ajax,data:n,processData:!1,contentType:!1,cache:!1,dataType:"json",success:function(n){200==n.status?t(n.msg):e(n.msg)},error:function(){e("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".delete-products",(function(t){t.preventDefault();var n=$(this).data("product-id"),e=$(this);Swal.fire({title:"Cảnh Báo",text:"Sau khi xoá tất cả dữ liệu liên quan đến đơn hàng này như lượt bán, lượt xem, dữ liệu khách hàng đã mua v.v... sẽ không còn tồn tại nữa. Bạn có chắc chắn muốn xoá?",icon:"warning",showCancelButton:!0,confirmButtonText:"Có, xoá ngay!",cancelButtonText:"Hủy bỏ"}).then((function(t){if(t.isConfirmed){Wsloader(!0);const t=new Promise((function(t,a){$.ajax({type:"POST",url:i.ajax,data:{action:"delete-product",id_product:n},dataType:"json",success:function(n){Wsloader(!1),200==n.status?(e.closest("tr").remove(),t(n.msg)):a(n.msg)},error:function(){a("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}));r(t)}}))})),i.ws.on("click",".discontinued-product",(function(t){t.preventDefault();var n=$(this).data("product-id"),e=$(this);o(new Promise((function(t,a){$.ajax({type:"POST",url:i.ajax,data:{action:"discontinued-product",id_product:n},dataType:"json",success:function(s){if(200===s.status){const a=1==s.new_status?"Ngừng Bán":"Mở Bán Lại";e.find("span").text(a);const i=$('.product-status[data-product-id="'+n+'"]');1==s.new_status?i.html('<span class="badge badge-dim rounded-pill bg-outline-success">Hoạt Động</span>'):i.html('<span class="badge badge-dim rounded-pill bg-outline-warning">Tạm Ngừng Bán</span>'),t(s.msg)}else a(s.msg)},error:function(){a("Mất kết nối đến máy chủ, vui lòng kiểm tra lại!")}})})))})),i.ws.on("click",".truncate-all-product",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Xác nhận hành động xoá tất cả đơn hàng, lưu ý không thể khôi phục lại đơn hàng khi bị xoá, bạn vẫn muốn tiếp tục?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{action:"truncate-all-product"},dataType:"json",success:function(e){200==e.status?($("table tbody").empty(),t(e.msg)):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))})),i.ws.on("click",".truncate-all-history-product",(function(t){t.preventDefault(),Swal.fire({title:"Cảnh Báo",text:"Xác nhận hành động xoá tất cả lịch sử mua, nếu khách hàng đã mua đơn hàng thì mọi dữ liệu bên phía họ cũng sẽ biến mất, bạn có muốn tiếp tục?",icon:"warning",showCancelButton:!0,cancelButtonText:"Hủy bỏ",confirmButtonText:"Có, xoá ngay!"}).then((t=>{t.isConfirmed&&(Wsloader(!0),o(new Promise((function(t,n){$.ajax({type:"post",url:i.ajax,data:{action:"truncate-all-history-product"},dataType:"json",success:function(e){200==e.status?t(e.msg):n(e.msg)},error:function(t,e,a){n("Mất kết nối đến máy chủ vui lòng kiểm tra lại!")}})}))))}))}))}$(document).ready(Administrator);