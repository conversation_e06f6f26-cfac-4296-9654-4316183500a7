<?php
// Simple debug script for login issues
require_once($_SERVER['DOCUMENT_ROOT'].'/config/database.php');

echo "<h2>Simple Login Debug</h2>";

// Test database connection
echo "<h3>Database Connection</h3>";
if ($thanhdieudb) {
    echo "✅ Database connection OK<br>";
    echo "Database: " . ($config['database'] ?? 'unknown') . "<br>";
    
    // Check if users table exists
    $result = $thanhdieudb->query("SHOW TABLES LIKE 'users'");
    if ($result && $result->num_rows > 0) {
        echo "✅ Users table exists<br>";
        
        // Get table structure
        echo "<h4>Table Structure:</h4>";
        $describe = $thanhdieudb->query("DESCRIBE users");
        if ($describe) {
            echo "<pre>";
            while($row = $describe->fetch_assoc()) {
                echo $row['Field'] . " - " . $row['Type'] . " - " . $row['Key'] . "\n";
            }
            echo "</pre>";
        }
        
        // Count users
        $count = $thanhdieudb->query("SELECT COUNT(*) as total FROM users");
        if ($count) {
            $total = $count->fetch_assoc();
            echo "Total users: " . $total['total'] . "<br>";
        }
        
        // Show first user (safe columns only)
        echo "<h4>Sample User Data:</h4>";
        $sample = $thanhdieudb->query("SELECT * FROM users LIMIT 1");
        if ($sample && $sample->num_rows > 0) {
            $user = $sample->fetch_assoc();
            echo "<pre>";
            foreach($user as $key => $value) {
                if ($key !== 'password') { // Don't show password
                    echo "$key: " . ($value ?? 'NULL') . "\n";
                }
            }
            echo "</pre>";
        }
        
    } else {
        echo "❌ Users table not found<br>";
        // Show all tables
        $all_tables = $thanhdieudb->query("SHOW TABLES");
        if ($all_tables) {
            echo "Available tables:<br>";
            while($table = $all_tables->fetch_row()) {
                echo "- " . $table[0] . "<br>";
            }
        }
    }
} else {
    echo "❌ Database connection failed<br>";
}

// Test localhost detection
echo "<h3>Environment</h3>";
$isLocalhost = (
    $_SERVER['HTTP_HOST'] === 'localhost' || 
    $_SERVER['HTTP_HOST'] === '127.0.0.1' || 
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
);
echo "Is localhost: " . ($isLocalhost ? 'YES' : 'NO') . "<br>";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";

// Test session
echo "<h3>Session Status</h3>";
echo "Session status: " . session_status() . "<br>";
echo "Session ID: " . (session_id() ?: 'none') . "<br>";

// Test cookies
echo "<h3>Cookies</h3>";
if (isset($_COOKIE['ssk'])) {
    echo "SSK cookie exists (length: " . strlen($_COOKIE['ssk']) . ")<br>";
} else {
    echo "No SSK cookie<br>";
}

// Test settings
echo "<h3>Settings</h3>";
if (class_exists('Settings')) {
    $TD = new Settings();
    echo "AES Key configured: " . (strlen($TD->Setting('key-aes') ?? '') > 0 ? 'YES' : 'NO') . "<br>";
    echo "Loader enabled: " . ($TD->Setting('loader') ? 'YES' : 'NO') . "<br>";
} else {
    echo "Settings class not available<br>";
}

echo "<hr>";
echo "<p><a href='/debug-login.php'>Back to full debug</a></p>";
?>