<?php require($_SERVER['DOCUMENT_ROOT'].'/config/database.php');?> <!doctype html><html lang="vi-VN"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0"><title>Bad Error - <?=$TD->Setting('title')?></title><link rel="icon" type="image/x-icon" href="<?=$TD->Setting('favicon')?>?v=<?=rand(1234,9999)?>"><style>html{margin:0;padding:0;background-color:#fff;-webkit-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}body,html{width:100%;height:100%;overflow:hidden}#svgContainer{width:640px;height:512px;background-color:#fff;position:absolute;top:0;left:0;right:0;bottom:0;margin:auto}h1{color:red;font-family:Arial,Helvetica,sans-serif;text-align:center;font-weight:800;margin-top:3em;font-size:clamp(2rem,2vw,5rem)}</style></head><body><h1><del>FUCK DEVTOOL</del></h1><script type="text/javascript" src="/<?=__LIBRARY__?>/bodymovin/bodymovin.js"></script><script type="text/javascript" src="/<?=__LIBRARY__?>/bodymovin/data.js?v=1"></script><div id="svgContainer"></div><script type="text/javascript">var svgContainer=document.getElementById("svgContainer"),animItem=bodymovin.loadAnimation({wrapper:svgContainer,animType:"svg",loop:!0,animationData:JSON.parse(animationData)}); <?php if ($TD->Setting('fuck-devtools')==1) {?>//window.location.replace("/");<?php }?> </script></body></html>